{"name": "import-in-the-middle", "version": "1.7.1", "description": "Intercept imports in Node.js", "main": "index.js", "scripts": {"test": "c8 --check-coverage --lines 85 imhotap --runner 'node test/runtest' --files test/{hook,low-level,other,get-esm-exports}/*", "test:ts": "c8 imhotap --runner 'node test/runtest' --files test/typescript/*.test.mts", "coverage": "c8 --reporter html imhotap --runner 'node test/runtest' --files test/{hook,low-level,other,get-esm-exports}/* && echo '\nNow open coverage/index.html\n'"}, "repository": {"type": "git", "url": "git+ssh://**************/DataDog/import-in-the-middle.git"}, "keywords": ["import", "ritm", "iitm", "loader", "hook", "hooks"], "author": "Bryan <PERSON> <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/DataDog/import-in-the-middle/issues"}, "homepage": "https://github.com/DataDog/import-in-the-middle#readme", "devDependencies": {"@types/node": "^18.0.6", "c8": "^7.8.0", "imhotap": "^2.1.0", "ts-node": "^10.9.1", "typescript": "^4.7.4"}, "dependencies": {"acorn": "^8.8.2", "acorn-import-assertions": "^1.9.0", "cjs-module-lexer": "^1.2.2", "module-details-from-path": "^1.0.3"}}