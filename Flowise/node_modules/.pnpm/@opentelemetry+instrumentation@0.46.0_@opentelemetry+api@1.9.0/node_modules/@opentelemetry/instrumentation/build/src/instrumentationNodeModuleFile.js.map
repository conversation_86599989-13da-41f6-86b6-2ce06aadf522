{"version": 3, "file": "instrumentationNodeModuleFile.js", "sourceRoot": "", "sources": ["../../src/instrumentationNodeModuleFile.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,+BAAiC;AAEjC,MAAa,6BAA6B;IAIxC,YACE,IAAY,EACL,iBAA2B,EAC3B,KAAsD,EACtD,OAA4D;QAF5D,sBAAiB,GAAjB,iBAAiB,CAAU;QAC3B,UAAK,GAAL,KAAK,CAAiD;QACtD,YAAO,GAAP,OAAO,CAAqD;QAEnE,IAAI,CAAC,IAAI,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;CACF;AAZD,sEAYC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationModuleFile } from './types';\nimport { normalize } from 'path';\n\nexport class InstrumentationNodeModuleFile<T>\n  implements InstrumentationModuleFile<T>\n{\n  public name: string;\n  constructor(\n    name: string,\n    public supportedVersions: string[],\n    public patch: (moduleExports: T, moduleVersion?: string) => T,\n    public unpatch: (moduleExports?: T, moduleVersion?: string) => void\n  ) {\n    this.name = normalize(name);\n  }\n}\n"]}