interface GitInfo {
    remoteUrl?: string | null;
    commit?: string | null;
    branch?: string | null;
    authorName?: string | null;
    authorEmail?: string | null;
    commitMessage?: string | null;
    commitTime?: string | null;
    dirty?: boolean | null;
    tags?: string | null;
}
export declare const getGitInfo: (remote?: string) => Promise<GitInfo | null>;
export declare const getDefaultRevisionId: () => Promise<string | null>;
export {};
