import type { RunTreeConfig } from "../index.js";
/**
 * Wrap a Vercel AI SDK model, enabling automatic LangSmith tracing.
 * After wrapping a model, you can use it with the Vercel AI SDK Core
 * methods as normal.
 *
 * @example
 * ```ts
 * import { anthropic } from "@ai-sdk/anthropic";
 * import { streamText } from "ai";
 * import { wrapAISDKModel } from "langsmith/wrappers/vercel";
 *
 * const anthropicModel = anthropic("claude-3-haiku-20240307");
 *
 * const modelWithTracing = wrapAISDKModel(anthropicModel);
 *
 * const { textStream } = await streamText({
 *   model: modelWithTracing,
 *   prompt: "Write a vegetarian lasagna recipe for 4 people.",
 * });
 *
 * for await (const chunk of textStream) {
 *   console.log(chunk);
 * }
 * ```
 * @param model An AI SDK model instance.
 * @param options LangSmith options.
 * @returns
 */
export declare const wrapAISDKModel: <T extends object>(model: T, options?: Partial<RunTreeConfig>) => T;
