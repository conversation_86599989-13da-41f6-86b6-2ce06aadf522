/**
 * Overrides the fetch implementation used for LangSmith calls.
 * You should use this if you need to use an implementation of fetch
 * other than the default global (e.g. for dealing with proxies).
 * @param fetch The new fetch functino to use.
 */
export declare const overrideFetchImplementation: (fetch: (...args: any[]) => any) => void;
export declare const _globalFetchImplementationIsNodeFetch: () => boolean;
