humanize-ms
---------------

[![NPM version][npm-image]][npm-url]
[![build status][travis-image]][travis-url]
[![Test coverage][coveralls-image]][coveralls-url]
[![Gittip][gittip-image]][gittip-url]
[![<PERSON>][david-image]][david-url]

[npm-image]: https://img.shields.io/npm/v/humanize-ms.svg?style=flat
[npm-url]: https://npmjs.org/package/humanize-ms
[travis-image]: https://img.shields.io/travis/node-modules/humanize-ms.svg?style=flat
[travis-url]: https://travis-ci.org/node-modules/humanize-ms
[coveralls-image]: https://img.shields.io/coveralls/node-modules/humanize-ms.svg?style=flat
[coveralls-url]: https://coveralls.io/r/node-modules/humanize-ms?branch=master
[gittip-image]: https://img.shields.io/gittip/dead-horse.svg?style=flat
[gittip-url]: https://www.gittip.com/dead-horse/
[david-image]: https://img.shields.io/david/node-modules/humanize-ms.svg?style=flat
[david-url]: https://david-dm.org/node-modules/humanize-ms

transform humanize time to ms

## Installation

```bash
$ npm install humanize-ms
```

## Examples

```js
var ms = require('humanize-ms');

ms('1s') // 1000
ms(1000) // 1000
```

### License

MIT
