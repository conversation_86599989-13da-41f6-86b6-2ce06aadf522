"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.retryWrapper = void 0;
const retryWrapper = (toRetry, maxRetries, delayMs) => {
    return async () => {
        for (let i = 0; i < maxRetries; ++i) {
            try {
                return await toRetry();
            }
            catch (e) {
                await new Promise((resolve) => setTimeout(resolve, delayMs));
            }
        }
        return await toRetry();
    };
};
exports.retryWrapper = retryWrapper;
