import { PromptTemplate } from "@langchain/core/prompts";
export declare const PREDICT_NEXT_USER_MESSAGE_PROMPT: PromptTemplate<import("@langchain/core/prompts").ParamsFromFString<"\nYou have been tasked with coming up with insights and data-points based on a chat history between a human and an AI.\nGiven the user's chat history provide the following:\n- Concise reasoning about the users internal mental state.\n- Your prediction on how they will respond to the AI's most recent message.\n- A concise list of any additional insights that would be useful to improve prediction.\n--------\nChat History: {chat_history}">, any>;
export declare const PREDICTION_VIOLATIONS_PROMPT: PromptTemplate<import("@langchain/core/prompts").ParamsFromFString<"You have been given a prediction and an actual message from a human and AI conversation.\nUsing the prediction, actual message, and additional user insights, generate the following:\n- How exactly was the original prediction violated? Which parts were wrong? State the exact differences.\n- If there were errors with the prediction, what were they and why?\n--------\nPredicted Output: {predicted_output}\n--------\nActual Output: {actual_output}\n--------\nUser Insights: {user_insights}\n--------\n">, any>;
export declare const GENERATE_REVISED_PREDICTION_PROMPT: PromptTemplate<import("@langchain/core/prompts").ParamsFromFString<"\nYou have been tasked with revising a prediction on what a user might say in a chat conversation.\n--------\nYour previous prediction: {prediction}\n--------\nWays in which your prediction was off: {explained_prediction_errors}\n--------\nKey insights to the user: {user_insights}\n--------\nGiven the above, revise your prediction to be more accurate.\nRevised Prediction:">, any>;
export declare const GENERATE_FACTS_PROMPT: PromptTemplate<import("@langchain/core/prompts").ParamsFromFString<"\nGiven a user message, an LLM generated prediction of what that message might be, and a list of violations which the prediction made compared to the actual message, generate a fact about the user, relevant to the users message.\n--------\nPrediction violations: {prediction_violations}\n--------\nRevised prediction: {prediction}\n--------\nActual user message: {user_message}\n--------\nRelevant fact:">, any>;
