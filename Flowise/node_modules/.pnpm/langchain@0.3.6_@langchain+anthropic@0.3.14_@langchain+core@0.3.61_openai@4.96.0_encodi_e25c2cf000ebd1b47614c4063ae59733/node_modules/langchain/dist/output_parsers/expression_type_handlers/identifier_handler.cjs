"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdentifierHandler = void 0;
const base_js_1 = require("./base.cjs");
/**
 * Handles identifiers in the LangChain Expression Language. Extends the
 * NodeHandler class.
 */
class IdentifierHandler extends base_js_1.NodeHandler {
    /**
     * Checks if a given node is an identifier. If it is, it returns the node;
     * otherwise, it returns false.
     * @param node The node to check.
     * @returns The node if it is an identifier, or false otherwise.
     */
    async accepts(node) {
        if (base_js_1.ASTParser.isIdentifier(node)) {
            return node;
        }
        else {
            return false;
        }
    }
    /**
     * Processes the identifier node. If the handler does not have a parent
     * handler, it throws an error. Otherwise, it extracts the name of the
     * identifier, removes any enclosing quotes, and returns an object of type
     * IdentifierType with the type set to "identifier" and the value set to
     * the extracted name.
     * @param node The identifier node to process.
     * @returns An object of type IdentifierType with the type set to "identifier" and the value set to the extracted name.
     */
    async handle(node) {
        if (!this.parentHandler) {
            throw new Error("ArrayLiteralExpressionHandler must have a parent handler");
        }
        const text = node.name.replace(/^["'](.+(?=["']$))["']$/, "$1");
        return { type: "identifier", value: text };
    }
}
exports.IdentifierHandler = IdentifierHandler;
