import { PromptTemplate } from "@langchain/core/prompts";
export declare const SUMMARY_PROMPT: PromptTemplate<{
    summary: any;
    new_lines: any;
}, any>;
export declare const _DEFAULT_ENTITY_MEMORY_CONVERSATION_TEMPLATE = "You are an assistant to a human, powered by a large language model trained by OpenAI.\n\nYou are designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, you are able to generate human-like text based on the input you receive, allowing you to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n\nYou are constantly learning and improving, and your capabilities are constantly evolving. You are able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. You have access to some personalized information provided by the human in the Context section below. Additionally, you are able to generate your own text based on the input you receive, allowing you to engage in discussions and provide explanations and descriptions on a wide range of topics.\n\nOverall, you are a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether the human needs help with a specific question or just wants to have a conversation about a particular topic, you are here to assist.\n\nContext:\n{entities}\n\nCurrent conversation:\n{history}\nLast line:\nHuman: {input}\nYou:";
export declare const ENTITY_MEMORY_CONVERSATION_TEMPLATE: PromptTemplate<{
    input: any;
    entities: any;
    history: any;
}, any>;
export declare const _DEFAULT_ENTITY_EXTRACTION_TEMPLATE = "You are an AI assistant reading the transcript of a conversation between an AI and a human. Extract all of the proper nouns from the last line of conversation. As a guideline, a proper noun is generally capitalized. You should definitely extract all names and places.\n\nThe conversation history is provided just in case of a coreference (e.g. \"What do you know about him\" where \"him\" is defined in a previous line) -- ignore items mentioned there that are not in the last line.\n\nReturn the output as a single comma-separated list, or NONE if there is nothing of note to return (e.g. the user is just issuing a greeting or having a simple conversation).\n\nEXAMPLE\nConversation history:\nPerson #1: my name is Jacob. how's it going today?\nAI: \"It's going great! How about you?\"\nPerson #1: good! busy working on Langchain. lots to do.\nAI: \"That sounds like a lot of work! What kind of things are you doing to make Langchain better?\"\nLast line:\nPerson #1: i'm trying to improve Langchain's interfaces, the UX, its integrations with various products the user might want ... a lot of stuff.\nOutput: Jacob,Langchain\nEND OF EXAMPLE\n\nEXAMPLE\nConversation history:\nPerson #1: how's it going today?\nAI: \"It's going great! How about you?\"\nPerson #1: good! busy working on Langchain. lots to do.\nAI: \"That sounds like a lot of work! What kind of things are you doing to make Langchain better?\"\nLast line:\nPerson #1: i'm trying to improve Langchain's interfaces, the UX, its integrations with various products the user might want ... a lot of stuff. I'm working with Person #2.\nOutput: Langchain, Person #2\nEND OF EXAMPLE\n\nConversation history (for reference only):\n{history}\nLast line of conversation (for extraction):\nHuman: {input}\n\nOutput:";
export declare const ENTITY_EXTRACTION_PROMPT: PromptTemplate<{
    input: any;
    history: any;
}, any>;
export declare const _DEFAULT_ENTITY_SUMMARIZATION_TEMPLATE = "You are an AI assistant helping a human keep track of facts about relevant people, places, and concepts in their life. Update and add to the summary of the provided entity in the \"Entity\" section based on the last line of your conversation with the human. If you are writing the summary for the first time, return a single sentence.\nThe update should only include facts that are relayed in the last line of conversation about the provided entity, and should only contain facts about the provided entity.\n\nIf there is no new information about the provided entity or the information is not worth noting (not an important or relevant fact to remember long-term), output the exact string \"UNCHANGED\" below.\n\nFull conversation history (for context):\n{history}\n\nEntity to summarize:\n{entity}\n\nExisting summary of {entity}:\n{summary}\n\nLast line of conversation:\nHuman: {input}\nUpdated summary (or the exact string \"UNCHANGED\" if there is no new information about {entity} above):";
export declare const ENTITY_SUMMARIZATION_PROMPT: PromptTemplate<{
    input: any;
    summary: any;
    history: any;
    entity: any;
}, any>;
