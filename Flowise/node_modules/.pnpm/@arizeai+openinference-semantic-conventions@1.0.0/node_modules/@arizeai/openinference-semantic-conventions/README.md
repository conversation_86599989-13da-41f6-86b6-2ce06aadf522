# OpenInference Semantic Conventions

[![npm version](https://badge.fury.io/js/@arizeai%2Fopeninference-semantic-conventions.svg)](https://badge.fury.io/js/@arizeai%2Fopeninference-semantic-conventions)

This package provides OpenInference semantic conventions for tracing of LLM Applications. Semantic Conventions define a common set of (semantic) attributes which provide meaning to data when collecting, producing and consuming it.
