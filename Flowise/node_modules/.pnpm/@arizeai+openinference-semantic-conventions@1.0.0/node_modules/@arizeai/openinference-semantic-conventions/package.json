{"name": "@arizeai/openinference-semantic-conventions", "version": "1.0.0", "private": false, "main": "dist/src/index.js", "module": "dist/esm/index.js", "esnext": "dist/esnext/index.js", "types": "dist/src/index.d.ts", "description": "This package provides OpenInference semantic conventions for tracing of LLM Applications.", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/src/index.js"}}, "files": ["dist", "src"], "keywords": [], "author": "<EMAIL>", "license": "Apache-2.0", "homepage": "https://github.com/arize-ai/openinference/tree/main/js/packages/openinference-semantic-conventions", "repository": {"type": "git", "url": "git+https://github.com/Arize-ai/openinference.git"}, "bugs": {"url": "https://github.com/Arize-ai/openinference/issues"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc --build tsconfig.json tsconfig.esm.json tsconfig.esnext.json && tsc-alias -p tsconfig.esm.json", "postbuild": "echo '{\"type\": \"module\"}' > ./dist/esm/package.json && rimraf dist/test", "type:check": "tsc --noEmit", "test": "echo \"NOOP: semantic conventions has no tests\" && exit 0"}}