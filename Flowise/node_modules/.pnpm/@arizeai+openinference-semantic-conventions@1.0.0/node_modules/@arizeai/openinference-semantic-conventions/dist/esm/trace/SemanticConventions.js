/**
 * Semantic conventions for OpenInference tracing
 */
export const SemanticAttributePrefixes = {
    input: "input",
    output: "output",
    llm: "llm",
    retrieval: "retrieval",
    reranker: "reranker",
    messages: "messages",
    message: "message",
    document: "document",
    embedding: "embedding",
    tool: "tool",
    tool_call: "tool_call",
    metadata: "metadata",
    tag: "tag",
    session: "session",
    user: "user",
    openinference: "openinference",
    message_content: "message_content",
    image: "image",
    audio: "audio",
};
export const LLMAttributePostfixes = {
    provider: "provider",
    system: "system",
    model_name: "model_name",
    token_count: "token_count",
    input_messages: "input_messages",
    output_messages: "output_messages",
    invocation_parameters: "invocation_parameters",
    prompts: "prompts",
    prompt_template: "prompt_template",
    function_call: "function_call",
    tools: "tools",
};
export const LLMPromptTemplateAttributePostfixes = {
    variables: "variables",
    template: "template",
};
export const RetrievalAttributePostfixes = {
    documents: "documents",
};
export const RerankerAttributePostfixes = {
    input_documents: "input_documents",
    output_documents: "output_documents",
    query: "query",
    model_name: "model_name",
    top_k: "top_k",
};
export const EmbeddingAttributePostfixes = {
    embeddings: "embeddings",
    text: "text",
    model_name: "model_name",
    vector: "vector",
};
export const ToolAttributePostfixes = {
    name: "name",
    description: "description",
    parameters: "parameters",
    json_schema: "json_schema",
};
export const MessageAttributePostfixes = {
    role: "role",
    content: "content",
    contents: "contents",
    name: "name",
    function_call_name: "function_call_name",
    function_call_arguments_json: "function_call_arguments_json",
    tool_calls: "tool_calls",
    tool_call_id: "tool_call_id",
};
export const MessageContentsAttributePostfixes = {
    type: "type",
    text: "text",
    image: "image",
};
export const ImageAttributesPostfixes = {
    url: "url",
};
export const ToolCallAttributePostfixes = {
    function_name: "function.name",
    function_arguments_json: "function.arguments",
    id: "id",
};
export const DocumentAttributePostfixes = {
    id: "id",
    content: "content",
    score: "score",
    metadata: "metadata",
};
export const TagAttributePostfixes = {
    tags: "tags",
};
export const SessionAttributePostfixes = {
    id: "id",
};
export const UserAttributePostfixes = {
    id: "id",
};
export const AudioAttributesPostfixes = {
    url: "url",
    mime_type: "mime_type",
    transcript: "transcript",
};
/**
 * The input to any span
 */
export const INPUT_VALUE = `${SemanticAttributePrefixes.input}.value`;
export const INPUT_MIME_TYPE = `${SemanticAttributePrefixes.input}.mime_type`;
/**
 * The output of any span
 */
export const OUTPUT_VALUE = `${SemanticAttributePrefixes.output}.value`;
export const OUTPUT_MIME_TYPE = `${SemanticAttributePrefixes.output}.mime_type`;
/**
 * The messages sent to the LLM for completions
 * Typically seen in OpenAI chat completions
 * @see https://beta.openai.com/docs/api-reference/completions/create
 */
export const LLM_INPUT_MESSAGES = `${SemanticAttributePrefixes.llm}.${LLMAttributePostfixes.input_messages}`;
/**
 * The prompts sent to the LLM for completions
 * Typically seen in OpenAI legacy completions
 * @see https://beta.openai.com/docs/api-reference/completions/create
 */
export const LLM_PROMPTS = `${SemanticAttributePrefixes.llm}.${LLMAttributePostfixes.prompts}`;
/**
 * The JSON representation of the parameters passed to the LLM
 */
export const LLM_INVOCATION_PARAMETERS = `${SemanticAttributePrefixes.llm}.${LLMAttributePostfixes.invocation_parameters}`;
/**
 * The messages received from the LLM for completions
 * Typically seen in OpenAI chat completions
 * @see https://platform.openai.com/docs/api-reference/chat/object#choices-message
 */
export const LLM_OUTPUT_MESSAGES = `${SemanticAttributePrefixes.llm}.${LLMAttributePostfixes.output_messages}`;
/**
 * The name of the LLM model
 */
export const LLM_MODEL_NAME = `${SemanticAttributePrefixes.llm}.${LLMAttributePostfixes.model_name}`;
/**
 * The provider of the inferences. E.g. the cloud provider
 */
export const LLM_PROVIDER = `${SemanticAttributePrefixes.llm}.${LLMAttributePostfixes.provider}`;
/**
 * The AI product as identified by the client or server
 */
export const LLM_SYSTEM = `${SemanticAttributePrefixes.llm}.${LLMAttributePostfixes.system}`;
/** Token count for the completion by the llm */
export const LLM_TOKEN_COUNT_COMPLETION = `${SemanticAttributePrefixes.llm}.${LLMAttributePostfixes.token_count}.completion`;
/** Token count for the prompt to the llm */
export const LLM_TOKEN_COUNT_PROMPT = `${SemanticAttributePrefixes.llm}.${LLMAttributePostfixes.token_count}.prompt`;
/** Token count for the entire transaction with the llm */
export const LLM_TOKEN_COUNT_TOTAL = `${SemanticAttributePrefixes.llm}.${LLMAttributePostfixes.token_count}.total`;
/**
 * The role that the LLM assumes the message is from
 * during the LLM invocation
 */
export const MESSAGE_ROLE = `${SemanticAttributePrefixes.message}.${MessageAttributePostfixes.role}`;
/**
 * The name of the message. This is only used for role 'function' where the name
 * of the function is captured in the name field and the parameters are captured in the
 * content.
 */
export const MESSAGE_NAME = `${SemanticAttributePrefixes.message}.${MessageAttributePostfixes.name}`;
/**
 * The tool calls generated by the model, such as function calls.
 */
export const MESSAGE_TOOL_CALLS = `${SemanticAttributePrefixes.message}.${MessageAttributePostfixes.tool_calls}`;
/**
 * The id of the tool call on a "tool" role message
 */
export const MESSAGE_TOOL_CALL_ID = `${SemanticAttributePrefixes.message}.${MessageAttributePostfixes.tool_call_id}`;
/**
 * tool_call.function.name
 */
export const TOOL_CALL_FUNCTION_NAME = `${SemanticAttributePrefixes.tool_call}.${ToolCallAttributePostfixes.function_name}`;
/**
 * tool_call.function.argument (JSON string)
 */
export const TOOL_CALL_FUNCTION_ARGUMENTS_JSON = `${SemanticAttributePrefixes.tool_call}.${ToolCallAttributePostfixes.function_arguments_json}`;
/**
 * The id of the tool call
 */
export const TOOL_CALL_ID = `${SemanticAttributePrefixes.tool_call}.${ToolCallAttributePostfixes.id}`;
/**
 * The LLM function call function name
 */
export const MESSAGE_FUNCTION_CALL_NAME = `${SemanticAttributePrefixes.message}.${MessageAttributePostfixes.function_call_name}`;
/**
 * The LLM function call function arguments in a json string
 */
export const MESSAGE_FUNCTION_CALL_ARGUMENTS_JSON = `${SemanticAttributePrefixes.message}.${MessageAttributePostfixes.function_call_arguments_json}`;
/**
 * The content of the message sent to the LLM
 */
export const MESSAGE_CONTENT = `${SemanticAttributePrefixes.message}.${MessageAttributePostfixes.content}`;
/**
 * The array of contents for the message sent to the LLM. Each element of the array is
 * an `message_content` object.
 */
export const MESSAGE_CONTENTS = `${SemanticAttributePrefixes.message}.${MessageAttributePostfixes.contents}`;
/**
 * The type of content sent to the LLM
 */
export const MESSAGE_CONTENT_TYPE = `${SemanticAttributePrefixes.message_content}.${MessageContentsAttributePostfixes.type}`;
/**
 * The text content of the message sent to the LLM
 */
export const MESSAGE_CONTENT_TEXT = `${SemanticAttributePrefixes.message_content}.${MessageContentsAttributePostfixes.text}`;
/**
 * The image content of the message sent to the LLM
 */
export const MESSAGE_CONTENT_IMAGE = `${SemanticAttributePrefixes.message_content}.${MessageContentsAttributePostfixes.image}`;
/**
 * The http or base64 link to the image
 */
export const IMAGE_URL = `${SemanticAttributePrefixes.image}.${ImageAttributesPostfixes.url}`;
export const DOCUMENT_ID = `${SemanticAttributePrefixes.document}.${DocumentAttributePostfixes.id}`;
export const DOCUMENT_CONTENT = `${SemanticAttributePrefixes.document}.${DocumentAttributePostfixes.content}`;
export const DOCUMENT_SCORE = `${SemanticAttributePrefixes.document}.${DocumentAttributePostfixes.score}`;
export const DOCUMENT_METADATA = `${SemanticAttributePrefixes.document}.${DocumentAttributePostfixes.metadata}`;
/**
 * The text that was embedded to create the vector
 */
export const EMBEDDING_TEXT = `${SemanticAttributePrefixes.embedding}.${EmbeddingAttributePostfixes.text}`;
/**
 * The name of the model that was used to create the vector
 */
export const EMBEDDING_MODEL_NAME = `${SemanticAttributePrefixes.embedding}.${EmbeddingAttributePostfixes.model_name}`;
/**
 * The embedding vector. Typically a high dimensional vector of floats or ints
 */
export const EMBEDDING_VECTOR = `${SemanticAttributePrefixes.embedding}.${EmbeddingAttributePostfixes.vector}`;
/**
 * The embedding list root
 */
export const EMBEDDING_EMBEDDINGS = `${SemanticAttributePrefixes.embedding}.${EmbeddingAttributePostfixes.embeddings}`;
/**
 * The retrieval documents list root
 */
export const RETRIEVAL_DOCUMENTS = `${SemanticAttributePrefixes.retrieval}.${RetrievalAttributePostfixes.documents}`;
const PROMPT_TEMPLATE_PREFIX = `${SemanticAttributePrefixes.llm}.${LLMAttributePostfixes.prompt_template}`;
/**
 * The JSON representation of the variables used in the prompt template
 */
export const PROMPT_TEMPLATE_VARIABLES = `${PROMPT_TEMPLATE_PREFIX}.variables`;
/**
 * A prompt template
 */
export const PROMPT_TEMPLATE_TEMPLATE = `${PROMPT_TEMPLATE_PREFIX}.template`;
/**
 * The JSON representation of a function call of an LLM
 */
export const LLM_FUNCTION_CALL = `${SemanticAttributePrefixes.llm}.${LLMAttributePostfixes.function_call}`;
/**
 * List of tools that are advertised to the LLM to be able to call
 */
export const LLM_TOOLS = `${SemanticAttributePrefixes.llm}.${LLMAttributePostfixes.tools}`;
/**
 * The name of a tool
 */
export const TOOL_NAME = `${SemanticAttributePrefixes.tool}.${ToolAttributePostfixes.name}`;
/**
 * The description of a tool
 */
export const TOOL_DESCRIPTION = `${SemanticAttributePrefixes.tool}.${ToolAttributePostfixes.description}`;
/**
 * The parameters of the tool represented as a JSON string
 */
export const TOOL_PARAMETERS = `${SemanticAttributePrefixes.tool}.${ToolAttributePostfixes.parameters}`;
/**
 * The json schema of a tool input, It is RECOMMENDED that this be in the
 * OpenAI tool calling format: https://platform.openai.com/docs/assistants/tools
 */
export const TOOL_JSON_SCHEMA = `${SemanticAttributePrefixes.tool}.${ToolAttributePostfixes.json_schema}`;
/**
 * The session id of a trace. Used to correlate spans in a single session.
 */
export const SESSION_ID = `${SemanticAttributePrefixes.session}.${SessionAttributePostfixes.id}`;
/**
 * The user id of a trace. Used to correlate spans for a single user.
 */
export const USER_ID = `${SemanticAttributePrefixes.user}.${UserAttributePostfixes.id}`;
/**
 * The documents used as input to the reranker
 */
export const RERANKER_INPUT_DOCUMENTS = `${SemanticAttributePrefixes.reranker}.${RerankerAttributePostfixes.input_documents}`;
/**
 * The documents output by the reranker
 */
export const RERANKER_OUTPUT_DOCUMENTS = `${SemanticAttributePrefixes.reranker}.${RerankerAttributePostfixes.output_documents}`;
/**
 * The query string for the reranker
 */
export const RERANKER_QUERY = `${SemanticAttributePrefixes.reranker}.${RerankerAttributePostfixes.query}`;
/**
 * The model name for the reranker
 */
export const RERANKER_MODEL_NAME = `${SemanticAttributePrefixes.reranker}.${RerankerAttributePostfixes.model_name}`;
/**
 * The top k parameter for the reranker
 */
export const RERANKER_TOP_K = `${SemanticAttributePrefixes.reranker}.${RerankerAttributePostfixes.top_k}`;
/**
 * Metadata for a span, used to store user-defined key-value pairs
 */
export const METADATA = "metadata";
/**
 * A prompt template version
 */
export const PROMPT_TEMPLATE_VERSION = `${PROMPT_TEMPLATE_PREFIX}.version`;
/**
 * The tags associated with a span
 */
export const TAG_TAGS = `${SemanticAttributePrefixes.tag}.${TagAttributePostfixes.tags}`;
/**
 * The url of an audio file
 */
export const AUDIO_URL = `${SemanticAttributePrefixes.audio}.${AudioAttributesPostfixes.url}`;
/**
 * The audio mime type
 */
export const AUDIO_MIME_TYPE = `${SemanticAttributePrefixes.audio}.${AudioAttributesPostfixes.mime_type}`;
/**
 * The audio transcript as text
 */
export const AUDIO_TRANSCRIPT = `${SemanticAttributePrefixes.audio}.${AudioAttributesPostfixes.transcript}`;
export const SemanticConventions = {
    IMAGE_URL,
    INPUT_VALUE,
    INPUT_MIME_TYPE,
    OUTPUT_VALUE,
    OUTPUT_MIME_TYPE,
    LLM_INPUT_MESSAGES,
    LLM_OUTPUT_MESSAGES,
    LLM_MODEL_NAME,
    LLM_PROMPTS,
    LLM_INVOCATION_PARAMETERS,
    LLM_TOKEN_COUNT_COMPLETION,
    LLM_TOKEN_COUNT_PROMPT,
    LLM_TOKEN_COUNT_TOTAL,
    LLM_SYSTEM,
    LLM_PROVIDER,
    LLM_TOOLS,
    MESSAGE_ROLE,
    MESSAGE_NAME,
    MESSAGE_TOOL_CALLS,
    MESSAGE_TOOL_CALL_ID,
    TOOL_CALL_ID,
    TOOL_CALL_FUNCTION_NAME,
    TOOL_CALL_FUNCTION_ARGUMENTS_JSON,
    MESSAGE_FUNCTION_CALL_NAME,
    MESSAGE_FUNCTION_CALL_ARGUMENTS_JSON,
    MESSAGE_CONTENT,
    MESSAGE_CONTENTS,
    MESSAGE_CONTENT_IMAGE,
    MESSAGE_CONTENT_TEXT,
    MESSAGE_CONTENT_TYPE,
    DOCUMENT_ID,
    DOCUMENT_CONTENT,
    DOCUMENT_SCORE,
    DOCUMENT_METADATA,
    EMBEDDING_EMBEDDINGS,
    EMBEDDING_TEXT,
    EMBEDDING_MODEL_NAME,
    EMBEDDING_VECTOR,
    TOOL_DESCRIPTION,
    TOOL_NAME,
    TOOL_PARAMETERS,
    TOOL_JSON_SCHEMA,
    PROMPT_TEMPLATE_VARIABLES,
    PROMPT_TEMPLATE_TEMPLATE,
    PROMPT_TEMPLATE_VERSION,
    RERANKER_INPUT_DOCUMENTS,
    RERANKER_OUTPUT_DOCUMENTS,
    RERANKER_QUERY,
    RERANKER_MODEL_NAME,
    RERANKER_TOP_K,
    LLM_FUNCTION_CALL,
    RETRIEVAL_DOCUMENTS,
    SESSION_ID,
    USER_ID,
    METADATA,
    TAG_TAGS,
    OPENINFERENCE_SPAN_KIND: `${SemanticAttributePrefixes.openinference}.span.kind`,
};
export var OpenInferenceSpanKind;
(function (OpenInferenceSpanKind) {
    OpenInferenceSpanKind["LLM"] = "LLM";
    OpenInferenceSpanKind["CHAIN"] = "CHAIN";
    OpenInferenceSpanKind["TOOL"] = "TOOL";
    OpenInferenceSpanKind["RETRIEVER"] = "RETRIEVER";
    OpenInferenceSpanKind["RERANKER"] = "RERANKER";
    OpenInferenceSpanKind["EMBEDDING"] = "EMBEDDING";
    OpenInferenceSpanKind["AGENT"] = "AGENT";
    OpenInferenceSpanKind["GUARDRAIL"] = "GUARDRAIL";
    OpenInferenceSpanKind["EVALUATOR"] = "EVALUATOR";
})(OpenInferenceSpanKind || (OpenInferenceSpanKind = {}));
/**
 * An enum of common mime types. Not exhaustive.
 */
export var MimeType;
(function (MimeType) {
    MimeType["TEXT"] = "text/plain";
    MimeType["JSON"] = "application/json";
    MimeType["AUDIO_WAV"] = "audio/wav";
})(MimeType || (MimeType = {}));
export var LLMSystem;
(function (LLMSystem) {
    LLMSystem["OPENAI"] = "openai";
    LLMSystem["ANTHROPIC"] = "anthropic";
    LLMSystem["MISTRALAI"] = "mistralai";
    LLMSystem["COHERE"] = "cohere";
    LLMSystem["VERTEXAI"] = "vertexai";
})(LLMSystem || (LLMSystem = {}));
export var LLMProvider;
(function (LLMProvider) {
    LLMProvider["OPENAI"] = "openai";
    LLMProvider["ANTHROPIC"] = "anthropic";
    LLMProvider["MISTRALAI"] = "mistralai";
    LLMProvider["COHERE"] = "cohere";
    // Cloud Providers of LLM systems
    LLMProvider["GOOGLE"] = "google";
    LLMProvider["AWS"] = "aws";
    LLMProvider["AZURE"] = "azure";
})(LLMProvider || (LLMProvider = {}));
//# sourceMappingURL=SemanticConventions.js.map