{"version": 3, "file": "SemanticConventions.js", "sourceRoot": "", "sources": ["../../../src/trace/SemanticConventions.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,MAAM,CAAC,MAAM,yBAAyB,GAAG;IACvC,KAAK,EAAE,OAAO;IACd,MAAM,EAAE,QAAQ;IAChB,GAAG,EAAE,KAAK;IACV,SAAS,EAAE,WAAW;IACtB,QAAQ,EAAE,UAAU;IACpB,QAAQ,EAAE,UAAU;IACpB,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE,UAAU;IACpB,SAAS,EAAE,WAAW;IACtB,IAAI,EAAE,MAAM;IACZ,SAAS,EAAE,WAAW;IACtB,QAAQ,EAAE,UAAU;IACpB,GAAG,EAAE,KAAK;IACV,OAAO,EAAE,SAAS;IAClB,IAAI,EAAE,MAAM;IACZ,aAAa,EAAE,eAAe;IAC9B,eAAe,EAAE,iBAAiB;IAClC,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;CACN,CAAC;AAEX,MAAM,CAAC,MAAM,qBAAqB,GAAG;IACnC,QAAQ,EAAE,UAAU;IACpB,MAAM,EAAE,QAAQ;IAChB,UAAU,EAAE,YAAY;IACxB,WAAW,EAAE,aAAa;IAC1B,cAAc,EAAE,gBAAgB;IAChC,eAAe,EAAE,iBAAiB;IAClC,qBAAqB,EAAE,uBAAuB;IAC9C,OAAO,EAAE,SAAS;IAClB,eAAe,EAAE,iBAAiB;IAClC,aAAa,EAAE,eAAe;IAC9B,KAAK,EAAE,OAAO;CACN,CAAC;AAEX,MAAM,CAAC,MAAM,mCAAmC,GAAG;IACjD,SAAS,EAAE,WAAW;IACtB,QAAQ,EAAE,UAAU;CACZ,CAAC;AAEX,MAAM,CAAC,MAAM,2BAA2B,GAAG;IACzC,SAAS,EAAE,WAAW;CACd,CAAC;AAEX,MAAM,CAAC,MAAM,0BAA0B,GAAG;IACxC,eAAe,EAAE,iBAAiB;IAClC,gBAAgB,EAAE,kBAAkB;IACpC,KAAK,EAAE,OAAO;IACd,UAAU,EAAE,YAAY;IACxB,KAAK,EAAE,OAAO;CACN,CAAC;AAEX,MAAM,CAAC,MAAM,2BAA2B,GAAG;IACzC,UAAU,EAAE,YAAY;IACxB,IAAI,EAAE,MAAM;IACZ,UAAU,EAAE,YAAY;IACxB,MAAM,EAAE,QAAQ;CACR,CAAC;AAEX,MAAM,CAAC,MAAM,sBAAsB,GAAG;IACpC,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,aAAa;IAC1B,UAAU,EAAE,YAAY;IACxB,WAAW,EAAE,aAAa;CAClB,CAAC;AAEX,MAAM,CAAC,MAAM,yBAAyB,GAAG;IACvC,IAAI,EAAE,MAAM;IACZ,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE,UAAU;IACpB,IAAI,EAAE,MAAM;IACZ,kBAAkB,EAAE,oBAAoB;IACxC,4BAA4B,EAAE,8BAA8B;IAC5D,UAAU,EAAE,YAAY;IACxB,YAAY,EAAE,cAAc;CACpB,CAAC;AAEX,MAAM,CAAC,MAAM,iCAAiC,GAAG;IAC/C,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE,OAAO;CACN,CAAC;AAEX,MAAM,CAAC,MAAM,wBAAwB,GAAG;IACtC,GAAG,EAAE,KAAK;CACF,CAAC;AAEX,MAAM,CAAC,MAAM,0BAA0B,GAAG;IACxC,aAAa,EAAE,eAAe;IAC9B,uBAAuB,EAAE,oBAAoB;IAC7C,EAAE,EAAE,IAAI;CACA,CAAC;AAEX,MAAM,CAAC,MAAM,0BAA0B,GAAG;IACxC,EAAE,EAAE,IAAI;IACR,OAAO,EAAE,SAAS;IAClB,KAAK,EAAE,OAAO;IACd,QAAQ,EAAE,UAAU;CACZ,CAAC;AAEX,MAAM,CAAC,MAAM,qBAAqB,GAAG;IACnC,IAAI,EAAE,MAAM;CACJ,CAAC;AAEX,MAAM,CAAC,MAAM,yBAAyB,GAAG;IACvC,EAAE,EAAE,IAAI;CACA,CAAC;AAEX,MAAM,CAAC,MAAM,sBAAsB,GAAG;IACpC,EAAE,EAAE,IAAI;CACA,CAAC;AAEX,MAAM,CAAC,MAAM,wBAAwB,GAAG;IACtC,GAAG,EAAE,KAAK;IACV,SAAS,EAAE,WAAW;IACtB,UAAU,EAAE,YAAY;CAChB,CAAC;AAEX;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,GAAG,yBAAyB,CAAC,KAAK,QAAiB,CAAC;AAC/E,MAAM,CAAC,MAAM,eAAe,GAC1B,GAAG,yBAAyB,CAAC,KAAK,YAAqB,CAAC;AAC1D;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GACvB,GAAG,yBAAyB,CAAC,MAAM,QAAiB,CAAC;AACvD,MAAM,CAAC,MAAM,gBAAgB,GAC3B,GAAG,yBAAyB,CAAC,MAAM,YAAqB,CAAC;AAC3D;;;;GAIG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAC7B,GAAG,yBAAyB,CAAC,GAAG,IAAI,qBAAqB,CAAC,cAAc,EAAW,CAAC;AAEtF;;;;GAIG;AACH,MAAM,CAAC,MAAM,WAAW,GACtB,GAAG,yBAAyB,CAAC,GAAG,IAAI,qBAAqB,CAAC,OAAO,EAAW,CAAC;AAE/E;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GACpC,GAAG,yBAAyB,CAAC,GAAG,IAAI,qBAAqB,CAAC,qBAAqB,EAAW,CAAC;AAE7F;;;;GAIG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAC9B,GAAG,yBAAyB,CAAC,GAAG,IAAI,qBAAqB,CAAC,eAAe,EAAW,CAAC;AAEvF;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GACzB,GAAG,yBAAyB,CAAC,GAAG,IAAI,qBAAqB,CAAC,UAAU,EAAW,CAAC;AAElF;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GACvB,GAAG,yBAAyB,CAAC,GAAG,IAAI,qBAAqB,CAAC,QAAQ,EAAW,CAAC;AAEhF;;GAEG;AACH,MAAM,CAAC,MAAM,UAAU,GACrB,GAAG,yBAAyB,CAAC,GAAG,IAAI,qBAAqB,CAAC,MAAM,EAAW,CAAC;AAE9E,gDAAgD;AAChD,MAAM,CAAC,MAAM,0BAA0B,GACrC,GAAG,yBAAyB,CAAC,GAAG,IAAI,qBAAqB,CAAC,WAAW,aAAsB,CAAC;AAE9F,4CAA4C;AAC5C,MAAM,CAAC,MAAM,sBAAsB,GACjC,GAAG,yBAAyB,CAAC,GAAG,IAAI,qBAAqB,CAAC,WAAW,SAAkB,CAAC;AAE1F,0DAA0D;AAC1D,MAAM,CAAC,MAAM,qBAAqB,GAChC,GAAG,yBAAyB,CAAC,GAAG,IAAI,qBAAqB,CAAC,WAAW,QAAiB,CAAC;AACzF;;;GAGG;AACH,MAAM,CAAC,MAAM,YAAY,GACvB,GAAG,yBAAyB,CAAC,OAAO,IAAI,yBAAyB,CAAC,IAAI,EAAW,CAAC;AAEpF;;;;GAIG;AACH,MAAM,CAAC,MAAM,YAAY,GACvB,GAAG,yBAAyB,CAAC,OAAO,IAAI,yBAAyB,CAAC,IAAI,EAAW,CAAC;AAEpF;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAC7B,GAAG,yBAAyB,CAAC,OAAO,IAAI,yBAAyB,CAAC,UAAU,EAAW,CAAC;AAE1F;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAC/B,GAAG,yBAAyB,CAAC,OAAO,IAAI,yBAAyB,CAAC,YAAY,EAAW,CAAC;AAE5F;;GAEG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAClC,GAAG,yBAAyB,CAAC,SAAS,IAAI,0BAA0B,CAAC,aAAa,EAAW,CAAC;AAEhG;;GAEG;AACH,MAAM,CAAC,MAAM,iCAAiC,GAC5C,GAAG,yBAAyB,CAAC,SAAS,IAAI,0BAA0B,CAAC,uBAAuB,EAAW,CAAC;AAE1G;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GACvB,GAAG,yBAAyB,CAAC,SAAS,IAAI,0BAA0B,CAAC,EAAE,EAAW,CAAC;AAErF;;GAEG;AACH,MAAM,CAAC,MAAM,0BAA0B,GACrC,GAAG,yBAAyB,CAAC,OAAO,IAAI,yBAAyB,CAAC,kBAAkB,EAAW,CAAC;AAElG;;GAEG;AACH,MAAM,CAAC,MAAM,oCAAoC,GAC/C,GAAG,yBAAyB,CAAC,OAAO,IAAI,yBAAyB,CAAC,4BAA4B,EAAW,CAAC;AAC5G;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAC1B,GAAG,yBAAyB,CAAC,OAAO,IAAI,yBAAyB,CAAC,OAAO,EAAW,CAAC;AACvF;;;GAGG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAC3B,GAAG,yBAAyB,CAAC,OAAO,IAAI,yBAAyB,CAAC,QAAQ,EAAW,CAAC;AACxF;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAC/B,GAAG,yBAAyB,CAAC,eAAe,IAAI,iCAAiC,CAAC,IAAI,EAAW,CAAC;AACpG;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAC/B,GAAG,yBAAyB,CAAC,eAAe,IAAI,iCAAiC,CAAC,IAAI,EAAW,CAAC;AACpG;;GAEG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAChC,GAAG,yBAAyB,CAAC,eAAe,IAAI,iCAAiC,CAAC,KAAK,EAAW,CAAC;AACrG;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GACpB,GAAG,yBAAyB,CAAC,KAAK,IAAI,wBAAwB,CAAC,GAAG,EAAW,CAAC;AAEhF,MAAM,CAAC,MAAM,WAAW,GACtB,GAAG,yBAAyB,CAAC,QAAQ,IAAI,0BAA0B,CAAC,EAAE,EAAW,CAAC;AAEpF,MAAM,CAAC,MAAM,gBAAgB,GAC3B,GAAG,yBAAyB,CAAC,QAAQ,IAAI,0BAA0B,CAAC,OAAO,EAAW,CAAC;AAEzF,MAAM,CAAC,MAAM,cAAc,GACzB,GAAG,yBAAyB,CAAC,QAAQ,IAAI,0BAA0B,CAAC,KAAK,EAAW,CAAC;AAEvF,MAAM,CAAC,MAAM,iBAAiB,GAC5B,GAAG,yBAAyB,CAAC,QAAQ,IAAI,0BAA0B,CAAC,QAAQ,EAAW,CAAC;AAE1F;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GACzB,GAAG,yBAAyB,CAAC,SAAS,IAAI,2BAA2B,CAAC,IAAI,EAAW,CAAC;AAExF;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAC/B,GAAG,yBAAyB,CAAC,SAAS,IAAI,2BAA2B,CAAC,UAAU,EAAW,CAAC;AAE9F;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAC3B,GAAG,yBAAyB,CAAC,SAAS,IAAI,2BAA2B,CAAC,MAAM,EAAW,CAAC;AAE1F;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAC/B,GAAG,yBAAyB,CAAC,SAAS,IAAI,2BAA2B,CAAC,UAAU,EAAW,CAAC;AAE9F;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAC9B,GAAG,yBAAyB,CAAC,SAAS,IAAI,2BAA2B,CAAC,SAAS,EAAW,CAAC;AAE7F,MAAM,sBAAsB,GAC1B,GAAG,yBAAyB,CAAC,GAAG,IAAI,qBAAqB,CAAC,eAAe,EAAW,CAAC;AAEvF;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GACpC,GAAG,sBAAsB,YAAqB,CAAC;AAEjD;;GAEG;AACH,MAAM,CAAC,MAAM,wBAAwB,GACnC,GAAG,sBAAsB,WAAoB,CAAC;AAEhD;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAC5B,GAAG,yBAAyB,CAAC,GAAG,IAAI,qBAAqB,CAAC,aAAa,EAAW,CAAC;AAErF;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GACpB,GAAG,yBAAyB,CAAC,GAAG,IAAI,qBAAqB,CAAC,KAAK,EAAW,CAAC;AAE7E;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GACpB,GAAG,yBAAyB,CAAC,IAAI,IAAI,sBAAsB,CAAC,IAAI,EAAW,CAAC;AAE9E;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAC3B,GAAG,yBAAyB,CAAC,IAAI,IAAI,sBAAsB,CAAC,WAAW,EAAW,CAAC;AAErF;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAC1B,GAAG,yBAAyB,CAAC,IAAI,IAAI,sBAAsB,CAAC,UAAU,EAAW,CAAC;AAEpF;;;GAGG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAC3B,GAAG,yBAAyB,CAAC,IAAI,IAAI,sBAAsB,CAAC,WAAW,EAAW,CAAC;AAErF;;GAEG;AACH,MAAM,CAAC,MAAM,UAAU,GACrB,GAAG,yBAAyB,CAAC,OAAO,IAAI,yBAAyB,CAAC,EAAE,EAAW,CAAC;AAElF;;GAEG;AACH,MAAM,CAAC,MAAM,OAAO,GAClB,GAAG,yBAAyB,CAAC,IAAI,IAAI,sBAAsB,CAAC,EAAE,EAAW,CAAC;AAE5E;;GAEG;AACH,MAAM,CAAC,MAAM,wBAAwB,GACnC,GAAG,yBAAyB,CAAC,QAAQ,IAAI,0BAA0B,CAAC,eAAe,EAAW,CAAC;AAEjG;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GACpC,GAAG,yBAAyB,CAAC,QAAQ,IAAI,0BAA0B,CAAC,gBAAgB,EAAW,CAAC;AAElG;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GACzB,GAAG,yBAAyB,CAAC,QAAQ,IAAI,0BAA0B,CAAC,KAAK,EAAW,CAAC;AAEvF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAC9B,GAAG,yBAAyB,CAAC,QAAQ,IAAI,0BAA0B,CAAC,UAAU,EAAW,CAAC;AAE5F;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GACzB,GAAG,yBAAyB,CAAC,QAAQ,IAAI,0BAA0B,CAAC,KAAK,EAAW,CAAC;AAEvF;;GAEG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,UAAmB,CAAC;AAE5C;;GAEG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAClC,GAAG,sBAAsB,UAAmB,CAAC;AAE/C;;GAEG;AACH,MAAM,CAAC,MAAM,QAAQ,GACnB,GAAG,yBAAyB,CAAC,GAAG,IAAI,qBAAqB,CAAC,IAAI,EAAW,CAAC;AAE5E;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GACpB,GAAG,yBAAyB,CAAC,KAAK,IAAI,wBAAwB,CAAC,GAAG,EAAW,CAAC;AAEhF;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAC1B,GAAG,yBAAyB,CAAC,KAAK,IAAI,wBAAwB,CAAC,SAAS,EAAW,CAAC;AAEtF;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAC3B,GAAG,yBAAyB,CAAC,KAAK,IAAI,wBAAwB,CAAC,UAAU,EAAW,CAAC;AAEvF,MAAM,CAAC,MAAM,mBAAmB,GAAG;IACjC,SAAS;IACT,WAAW;IACX,eAAe;IACf,YAAY;IACZ,gBAAgB;IAChB,kBAAkB;IAClB,mBAAmB;IACnB,cAAc;IACd,WAAW;IACX,yBAAyB;IACzB,0BAA0B;IAC1B,sBAAsB;IACtB,qBAAqB;IACrB,UAAU;IACV,YAAY;IACZ,SAAS;IACT,YAAY;IACZ,YAAY;IACZ,kBAAkB;IAClB,oBAAoB;IACpB,YAAY;IACZ,uBAAuB;IACvB,iCAAiC;IACjC,0BAA0B;IAC1B,oCAAoC;IACpC,eAAe;IACf,gBAAgB;IAChB,qBAAqB;IACrB,oBAAoB;IACpB,oBAAoB;IACpB,WAAW;IACX,gBAAgB;IAChB,cAAc;IACd,iBAAiB;IACjB,oBAAoB;IACpB,cAAc;IACd,oBAAoB;IACpB,gBAAgB;IAChB,gBAAgB;IAChB,SAAS;IACT,eAAe;IACf,gBAAgB;IAChB,yBAAyB;IACzB,wBAAwB;IACxB,uBAAuB;IACvB,wBAAwB;IACxB,yBAAyB;IACzB,cAAc;IACd,mBAAmB;IACnB,cAAc;IACd,iBAAiB;IACjB,mBAAmB;IACnB,UAAU;IACV,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,uBAAuB,EAAE,GAAG,yBAAyB,CAAC,aAAa,YAAY;CACvE,CAAC;AAEX,MAAM,CAAN,IAAY,qBAUX;AAVD,WAAY,qBAAqB;IAC/B,oCAAW,CAAA;IACX,wCAAe,CAAA;IACf,sCAAa,CAAA;IACb,gDAAuB,CAAA;IACvB,8CAAqB,CAAA;IACrB,gDAAuB,CAAA;IACvB,wCAAe,CAAA;IACf,gDAAuB,CAAA;IACvB,gDAAuB,CAAA;AACzB,CAAC,EAVW,qBAAqB,KAArB,qBAAqB,QAUhC;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,QAIX;AAJD,WAAY,QAAQ;IAClB,+BAAmB,CAAA;IACnB,qCAAyB,CAAA;IACzB,mCAAuB,CAAA;AACzB,CAAC,EAJW,QAAQ,KAAR,QAAQ,QAInB;AAED,MAAM,CAAN,IAAY,SAMX;AAND,WAAY,SAAS;IACnB,8BAAiB,CAAA;IACjB,oCAAuB,CAAA;IACvB,oCAAuB,CAAA;IACvB,8BAAiB,CAAA;IACjB,kCAAqB,CAAA;AACvB,CAAC,EANW,SAAS,KAAT,SAAS,QAMpB;AAED,MAAM,CAAN,IAAY,WASX;AATD,WAAY,WAAW;IACrB,gCAAiB,CAAA;IACjB,sCAAuB,CAAA;IACvB,sCAAuB,CAAA;IACvB,gCAAiB,CAAA;IACjB,iCAAiC;IACjC,gCAAiB,CAAA;IACjB,0BAAW,CAAA;IACX,8BAAe,CAAA;AACjB,CAAC,EATW,WAAW,KAAX,WAAW,QAStB"}