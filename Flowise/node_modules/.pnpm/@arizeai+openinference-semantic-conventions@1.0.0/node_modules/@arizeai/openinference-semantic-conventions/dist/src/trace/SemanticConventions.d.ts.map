{"version": 3, "file": "SemanticConventions.d.ts", "sourceRoot": "", "sources": ["../../../src/trace/SemanticConventions.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,eAAO,MAAM,yBAAyB;;;;;;;;;;;;;;;;;;;;CAoB5B,CAAC;AAEX,eAAO,MAAM,qBAAqB;;;;;;;;;;;;CAYxB,CAAC;AAEX,eAAO,MAAM,mCAAmC;;;CAGtC,CAAC;AAEX,eAAO,MAAM,2BAA2B;;CAE9B,CAAC;AAEX,eAAO,MAAM,0BAA0B;;;;;;CAM7B,CAAC;AAEX,eAAO,MAAM,2BAA2B;;;;;CAK9B,CAAC;AAEX,eAAO,MAAM,sBAAsB;;;;;CAKzB,CAAC;AAEX,eAAO,MAAM,yBAAyB;;;;;;;;;CAS5B,CAAC;AAEX,eAAO,MAAM,iCAAiC;;;;CAIpC,CAAC;AAEX,eAAO,MAAM,wBAAwB;;CAE3B,CAAC;AAEX,eAAO,MAAM,0BAA0B;;;;CAI7B,CAAC;AAEX,eAAO,MAAM,0BAA0B;;;;;CAK7B,CAAC;AAEX,eAAO,MAAM,qBAAqB;;CAExB,CAAC;AAEX,eAAO,MAAM,yBAAyB;;CAE5B,CAAC;AAEX,eAAO,MAAM,sBAAsB;;CAEzB,CAAC;AAEX,eAAO,MAAM,wBAAwB;;;;CAI3B,CAAC;AAEX;;GAEG;AACH,eAAO,MAAM,WAAW,eAAsD,CAAC;AAC/E,eAAO,MAAM,eAAe,mBAC6B,CAAC;AAC1D;;GAEG;AACH,eAAO,MAAM,YAAY,gBAC6B,CAAC;AACvD,eAAO,MAAM,gBAAgB,oBAC6B,CAAC;AAC3D;;;;GAIG;AACH,eAAO,MAAM,kBAAkB,sBACsD,CAAC;AAEtF;;;;GAIG;AACH,eAAO,MAAM,WAAW,eACsD,CAAC;AAE/E;;GAEG;AACH,eAAO,MAAM,yBAAyB,6BACsD,CAAC;AAE7F;;;;GAIG;AACH,eAAO,MAAM,mBAAmB,uBACsD,CAAC;AAEvF;;GAEG;AACH,eAAO,MAAM,cAAc,kBACsD,CAAC;AAElF;;GAEG;AACH,eAAO,MAAM,YAAY,gBACsD,CAAC;AAEhF;;GAEG;AACH,eAAO,MAAM,UAAU,cACsD,CAAC;AAE9E,gDAAgD;AAChD,eAAO,MAAM,0BAA0B,8BACsD,CAAC;AAE9F,4CAA4C;AAC5C,eAAO,MAAM,sBAAsB,0BACsD,CAAC;AAE1F,0DAA0D;AAC1D,eAAO,MAAM,qBAAqB,yBACsD,CAAC;AACzF;;;GAGG;AACH,eAAO,MAAM,YAAY,gBAC0D,CAAC;AAEpF;;;;GAIG;AACH,eAAO,MAAM,YAAY,gBAC0D,CAAC;AAEpF;;GAEG;AACH,eAAO,MAAM,kBAAkB,sBAC0D,CAAC;AAE1F;;GAEG;AACH,eAAO,MAAM,oBAAoB,wBAC0D,CAAC;AAE5F;;GAEG;AACH,eAAO,MAAM,uBAAuB,2BAC2D,CAAC;AAEhG;;GAEG;AACH,eAAO,MAAM,iCAAiC,gCAC2D,CAAC;AAE1G;;GAEG;AACH,eAAO,MAAM,YAAY,gBAC2D,CAAC;AAErF;;GAEG;AACH,eAAO,MAAM,0BAA0B,8BAC0D,CAAC;AAElG;;GAEG;AACH,eAAO,MAAM,oCAAoC,wCAC0D,CAAC;AAC5G;;GAEG;AACH,eAAO,MAAM,eAAe,mBAC0D,CAAC;AACvF;;;GAGG;AACH,eAAO,MAAM,gBAAgB,oBAC0D,CAAC;AACxF;;GAEG;AACH,eAAO,MAAM,oBAAoB,wBACkE,CAAC;AACpG;;GAEG;AACH,eAAO,MAAM,oBAAoB,wBACkE,CAAC;AACpG;;GAEG;AACH,eAAO,MAAM,qBAAqB,yBACkE,CAAC;AACrG;;GAEG;AACH,eAAO,MAAM,SAAS,aACyD,CAAC;AAEhF,eAAO,MAAM,WAAW,eAC2D,CAAC;AAEpF,eAAO,MAAM,gBAAgB,oBAC2D,CAAC;AAEzF,eAAO,MAAM,cAAc,kBAC2D,CAAC;AAEvF,eAAO,MAAM,iBAAiB,qBAC2D,CAAC;AAE1F;;GAEG;AACH,eAAO,MAAM,cAAc,kBAC4D,CAAC;AAExF;;GAEG;AACH,eAAO,MAAM,oBAAoB,wBAC4D,CAAC;AAE9F;;GAEG;AACH,eAAO,MAAM,gBAAgB,oBAC4D,CAAC;AAE1F;;GAEG;AACH,eAAO,MAAM,oBAAoB,wBAC4D,CAAC;AAE9F;;GAEG;AACH,eAAO,MAAM,mBAAmB,uBAC4D,CAAC;AAK7F;;GAEG;AACH,eAAO,MAAM,yBAAyB,iCACU,CAAC;AAEjD;;GAEG;AACH,eAAO,MAAM,wBAAwB,gCACU,CAAC;AAEhD;;GAEG;AACH,eAAO,MAAM,iBAAiB,qBACsD,CAAC;AAErF;;GAEG;AACH,eAAO,MAAM,SAAS,aACsD,CAAC;AAE7E;;GAEG;AACH,eAAO,MAAM,SAAS,aACuD,CAAC;AAE9E;;GAEG;AACH,eAAO,MAAM,gBAAgB,oBACuD,CAAC;AAErF;;GAEG;AACH,eAAO,MAAM,eAAe,mBACuD,CAAC;AAEpF;;;GAGG;AACH,eAAO,MAAM,gBAAgB,oBACuD,CAAC;AAErF;;GAEG;AACH,eAAO,MAAM,UAAU,cAC0D,CAAC;AAElF;;GAEG;AACH,eAAO,MAAM,OAAO,WACuD,CAAC;AAE5E;;GAEG;AACH,eAAO,MAAM,wBAAwB,4BAC2D,CAAC;AAEjG;;GAEG;AACH,eAAO,MAAM,yBAAyB,6BAC2D,CAAC;AAElG;;GAEG;AACH,eAAO,MAAM,cAAc,kBAC2D,CAAC;AAEvF;;GAEG;AACH,eAAO,MAAM,mBAAmB,uBAC2D,CAAC;AAE5F;;GAEG;AACH,eAAO,MAAM,cAAc,kBAC2D,CAAC;AAEvF;;GAEG;AACH,eAAO,MAAM,QAAQ,YAAsB,CAAC;AAE5C;;GAEG;AACH,eAAO,MAAM,uBAAuB,+BACU,CAAC;AAE/C;;GAEG;AACH,eAAO,MAAM,QAAQ,YACsD,CAAC;AAE5E;;GAEG;AACH,eAAO,MAAM,SAAS,aACyD,CAAC;AAEhF;;GAEG;AACH,eAAO,MAAM,eAAe,mBACyD,CAAC;AAEtF;;GAEG;AACH,eAAO,MAAM,gBAAgB,oBACyD,CAAC;AAEvF,eAAO,MAAM,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA0DtB,CAAC;AAEX,oBAAY,qBAAqB;IAC/B,GAAG,QAAQ;IACX,KAAK,UAAU;IACf,IAAI,SAAS;IACb,SAAS,cAAc;IACvB,QAAQ,aAAa;IACrB,SAAS,cAAc;IACvB,KAAK,UAAU;IACf,SAAS,cAAc;IACvB,SAAS,cAAc;CACxB;AAED;;GAEG;AACH,oBAAY,QAAQ;IAClB,IAAI,eAAe;IACnB,IAAI,qBAAqB;IACzB,SAAS,cAAc;CACxB;AAED,oBAAY,SAAS;IACnB,MAAM,WAAW;IACjB,SAAS,cAAc;IACvB,SAAS,cAAc;IACvB,MAAM,WAAW;IACjB,QAAQ,aAAa;CACtB;AAED,oBAAY,WAAW;IACrB,MAAM,WAAW;IACjB,SAAS,cAAc;IACvB,SAAS,cAAc;IACvB,MAAM,WAAW;IAEjB,MAAM,WAAW;IACjB,GAAG,QAAQ;IACX,KAAK,UAAU;CAChB"}