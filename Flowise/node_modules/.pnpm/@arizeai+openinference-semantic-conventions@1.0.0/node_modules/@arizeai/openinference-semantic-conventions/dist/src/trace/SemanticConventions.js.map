{"version": 3, "file": "SemanticConventions.js", "sourceRoot": "", "sources": ["../../../src/trace/SemanticConventions.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;AAEU,QAAA,yBAAyB,GAAG;IACvC,KAAK,EAAE,OAAO;IACd,MAAM,EAAE,QAAQ;IAChB,GAAG,EAAE,KAAK;IACV,SAAS,EAAE,WAAW;IACtB,QAAQ,EAAE,UAAU;IACpB,QAAQ,EAAE,UAAU;IACpB,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE,UAAU;IACpB,SAAS,EAAE,WAAW;IACtB,IAAI,EAAE,MAAM;IACZ,SAAS,EAAE,WAAW;IACtB,QAAQ,EAAE,UAAU;IACpB,GAAG,EAAE,KAAK;IACV,OAAO,EAAE,SAAS;IAClB,IAAI,EAAE,MAAM;IACZ,aAAa,EAAE,eAAe;IAC9B,eAAe,EAAE,iBAAiB;IAClC,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;CACN,CAAC;AAEE,QAAA,qBAAqB,GAAG;IACnC,QAAQ,EAAE,UAAU;IACpB,MAAM,EAAE,QAAQ;IAChB,UAAU,EAAE,YAAY;IACxB,WAAW,EAAE,aAAa;IAC1B,cAAc,EAAE,gBAAgB;IAChC,eAAe,EAAE,iBAAiB;IAClC,qBAAqB,EAAE,uBAAuB;IAC9C,OAAO,EAAE,SAAS;IAClB,eAAe,EAAE,iBAAiB;IAClC,aAAa,EAAE,eAAe;IAC9B,KAAK,EAAE,OAAO;CACN,CAAC;AAEE,QAAA,mCAAmC,GAAG;IACjD,SAAS,EAAE,WAAW;IACtB,QAAQ,EAAE,UAAU;CACZ,CAAC;AAEE,QAAA,2BAA2B,GAAG;IACzC,SAAS,EAAE,WAAW;CACd,CAAC;AAEE,QAAA,0BAA0B,GAAG;IACxC,eAAe,EAAE,iBAAiB;IAClC,gBAAgB,EAAE,kBAAkB;IACpC,KAAK,EAAE,OAAO;IACd,UAAU,EAAE,YAAY;IACxB,KAAK,EAAE,OAAO;CACN,CAAC;AAEE,QAAA,2BAA2B,GAAG;IACzC,UAAU,EAAE,YAAY;IACxB,IAAI,EAAE,MAAM;IACZ,UAAU,EAAE,YAAY;IACxB,MAAM,EAAE,QAAQ;CACR,CAAC;AAEE,QAAA,sBAAsB,GAAG;IACpC,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,aAAa;IAC1B,UAAU,EAAE,YAAY;IACxB,WAAW,EAAE,aAAa;CAClB,CAAC;AAEE,QAAA,yBAAyB,GAAG;IACvC,IAAI,EAAE,MAAM;IACZ,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE,UAAU;IACpB,IAAI,EAAE,MAAM;IACZ,kBAAkB,EAAE,oBAAoB;IACxC,4BAA4B,EAAE,8BAA8B;IAC5D,UAAU,EAAE,YAAY;IACxB,YAAY,EAAE,cAAc;CACpB,CAAC;AAEE,QAAA,iCAAiC,GAAG;IAC/C,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE,OAAO;CACN,CAAC;AAEE,QAAA,wBAAwB,GAAG;IACtC,GAAG,EAAE,KAAK;CACF,CAAC;AAEE,QAAA,0BAA0B,GAAG;IACxC,aAAa,EAAE,eAAe;IAC9B,uBAAuB,EAAE,oBAAoB;IAC7C,EAAE,EAAE,IAAI;CACA,CAAC;AAEE,QAAA,0BAA0B,GAAG;IACxC,EAAE,EAAE,IAAI;IACR,OAAO,EAAE,SAAS;IAClB,KAAK,EAAE,OAAO;IACd,QAAQ,EAAE,UAAU;CACZ,CAAC;AAEE,QAAA,qBAAqB,GAAG;IACnC,IAAI,EAAE,MAAM;CACJ,CAAC;AAEE,QAAA,yBAAyB,GAAG;IACvC,EAAE,EAAE,IAAI;CACA,CAAC;AAEE,QAAA,sBAAsB,GAAG;IACpC,EAAE,EAAE,IAAI;CACA,CAAC;AAEE,QAAA,wBAAwB,GAAG;IACtC,GAAG,EAAE,KAAK;IACV,SAAS,EAAE,WAAW;IACtB,UAAU,EAAE,YAAY;CAChB,CAAC;AAEX;;GAEG;AACU,QAAA,WAAW,GAAG,GAAG,iCAAyB,CAAC,KAAK,QAAiB,CAAC;AAClE,QAAA,eAAe,GAC1B,GAAG,iCAAyB,CAAC,KAAK,YAAqB,CAAC;AAC1D;;GAEG;AACU,QAAA,YAAY,GACvB,GAAG,iCAAyB,CAAC,MAAM,QAAiB,CAAC;AAC1C,QAAA,gBAAgB,GAC3B,GAAG,iCAAyB,CAAC,MAAM,YAAqB,CAAC;AAC3D;;;;GAIG;AACU,QAAA,kBAAkB,GAC7B,GAAG,iCAAyB,CAAC,GAAG,IAAI,6BAAqB,CAAC,cAAc,EAAW,CAAC;AAEtF;;;;GAIG;AACU,QAAA,WAAW,GACtB,GAAG,iCAAyB,CAAC,GAAG,IAAI,6BAAqB,CAAC,OAAO,EAAW,CAAC;AAE/E;;GAEG;AACU,QAAA,yBAAyB,GACpC,GAAG,iCAAyB,CAAC,GAAG,IAAI,6BAAqB,CAAC,qBAAqB,EAAW,CAAC;AAE7F;;;;GAIG;AACU,QAAA,mBAAmB,GAC9B,GAAG,iCAAyB,CAAC,GAAG,IAAI,6BAAqB,CAAC,eAAe,EAAW,CAAC;AAEvF;;GAEG;AACU,QAAA,cAAc,GACzB,GAAG,iCAAyB,CAAC,GAAG,IAAI,6BAAqB,CAAC,UAAU,EAAW,CAAC;AAElF;;GAEG;AACU,QAAA,YAAY,GACvB,GAAG,iCAAyB,CAAC,GAAG,IAAI,6BAAqB,CAAC,QAAQ,EAAW,CAAC;AAEhF;;GAEG;AACU,QAAA,UAAU,GACrB,GAAG,iCAAyB,CAAC,GAAG,IAAI,6BAAqB,CAAC,MAAM,EAAW,CAAC;AAE9E,gDAAgD;AACnC,QAAA,0BAA0B,GACrC,GAAG,iCAAyB,CAAC,GAAG,IAAI,6BAAqB,CAAC,WAAW,aAAsB,CAAC;AAE9F,4CAA4C;AAC/B,QAAA,sBAAsB,GACjC,GAAG,iCAAyB,CAAC,GAAG,IAAI,6BAAqB,CAAC,WAAW,SAAkB,CAAC;AAE1F,0DAA0D;AAC7C,QAAA,qBAAqB,GAChC,GAAG,iCAAyB,CAAC,GAAG,IAAI,6BAAqB,CAAC,WAAW,QAAiB,CAAC;AACzF;;;GAGG;AACU,QAAA,YAAY,GACvB,GAAG,iCAAyB,CAAC,OAAO,IAAI,iCAAyB,CAAC,IAAI,EAAW,CAAC;AAEpF;;;;GAIG;AACU,QAAA,YAAY,GACvB,GAAG,iCAAyB,CAAC,OAAO,IAAI,iCAAyB,CAAC,IAAI,EAAW,CAAC;AAEpF;;GAEG;AACU,QAAA,kBAAkB,GAC7B,GAAG,iCAAyB,CAAC,OAAO,IAAI,iCAAyB,CAAC,UAAU,EAAW,CAAC;AAE1F;;GAEG;AACU,QAAA,oBAAoB,GAC/B,GAAG,iCAAyB,CAAC,OAAO,IAAI,iCAAyB,CAAC,YAAY,EAAW,CAAC;AAE5F;;GAEG;AACU,QAAA,uBAAuB,GAClC,GAAG,iCAAyB,CAAC,SAAS,IAAI,kCAA0B,CAAC,aAAa,EAAW,CAAC;AAEhG;;GAEG;AACU,QAAA,iCAAiC,GAC5C,GAAG,iCAAyB,CAAC,SAAS,IAAI,kCAA0B,CAAC,uBAAuB,EAAW,CAAC;AAE1G;;GAEG;AACU,QAAA,YAAY,GACvB,GAAG,iCAAyB,CAAC,SAAS,IAAI,kCAA0B,CAAC,EAAE,EAAW,CAAC;AAErF;;GAEG;AACU,QAAA,0BAA0B,GACrC,GAAG,iCAAyB,CAAC,OAAO,IAAI,iCAAyB,CAAC,kBAAkB,EAAW,CAAC;AAElG;;GAEG;AACU,QAAA,oCAAoC,GAC/C,GAAG,iCAAyB,CAAC,OAAO,IAAI,iCAAyB,CAAC,4BAA4B,EAAW,CAAC;AAC5G;;GAEG;AACU,QAAA,eAAe,GAC1B,GAAG,iCAAyB,CAAC,OAAO,IAAI,iCAAyB,CAAC,OAAO,EAAW,CAAC;AACvF;;;GAGG;AACU,QAAA,gBAAgB,GAC3B,GAAG,iCAAyB,CAAC,OAAO,IAAI,iCAAyB,CAAC,QAAQ,EAAW,CAAC;AACxF;;GAEG;AACU,QAAA,oBAAoB,GAC/B,GAAG,iCAAyB,CAAC,eAAe,IAAI,yCAAiC,CAAC,IAAI,EAAW,CAAC;AACpG;;GAEG;AACU,QAAA,oBAAoB,GAC/B,GAAG,iCAAyB,CAAC,eAAe,IAAI,yCAAiC,CAAC,IAAI,EAAW,CAAC;AACpG;;GAEG;AACU,QAAA,qBAAqB,GAChC,GAAG,iCAAyB,CAAC,eAAe,IAAI,yCAAiC,CAAC,KAAK,EAAW,CAAC;AACrG;;GAEG;AACU,QAAA,SAAS,GACpB,GAAG,iCAAyB,CAAC,KAAK,IAAI,gCAAwB,CAAC,GAAG,EAAW,CAAC;AAEnE,QAAA,WAAW,GACtB,GAAG,iCAAyB,CAAC,QAAQ,IAAI,kCAA0B,CAAC,EAAE,EAAW,CAAC;AAEvE,QAAA,gBAAgB,GAC3B,GAAG,iCAAyB,CAAC,QAAQ,IAAI,kCAA0B,CAAC,OAAO,EAAW,CAAC;AAE5E,QAAA,cAAc,GACzB,GAAG,iCAAyB,CAAC,QAAQ,IAAI,kCAA0B,CAAC,KAAK,EAAW,CAAC;AAE1E,QAAA,iBAAiB,GAC5B,GAAG,iCAAyB,CAAC,QAAQ,IAAI,kCAA0B,CAAC,QAAQ,EAAW,CAAC;AAE1F;;GAEG;AACU,QAAA,cAAc,GACzB,GAAG,iCAAyB,CAAC,SAAS,IAAI,mCAA2B,CAAC,IAAI,EAAW,CAAC;AAExF;;GAEG;AACU,QAAA,oBAAoB,GAC/B,GAAG,iCAAyB,CAAC,SAAS,IAAI,mCAA2B,CAAC,UAAU,EAAW,CAAC;AAE9F;;GAEG;AACU,QAAA,gBAAgB,GAC3B,GAAG,iCAAyB,CAAC,SAAS,IAAI,mCAA2B,CAAC,MAAM,EAAW,CAAC;AAE1F;;GAEG;AACU,QAAA,oBAAoB,GAC/B,GAAG,iCAAyB,CAAC,SAAS,IAAI,mCAA2B,CAAC,UAAU,EAAW,CAAC;AAE9F;;GAEG;AACU,QAAA,mBAAmB,GAC9B,GAAG,iCAAyB,CAAC,SAAS,IAAI,mCAA2B,CAAC,SAAS,EAAW,CAAC;AAE7F,MAAM,sBAAsB,GAC1B,GAAG,iCAAyB,CAAC,GAAG,IAAI,6BAAqB,CAAC,eAAe,EAAW,CAAC;AAEvF;;GAEG;AACU,QAAA,yBAAyB,GACpC,GAAG,sBAAsB,YAAqB,CAAC;AAEjD;;GAEG;AACU,QAAA,wBAAwB,GACnC,GAAG,sBAAsB,WAAoB,CAAC;AAEhD;;GAEG;AACU,QAAA,iBAAiB,GAC5B,GAAG,iCAAyB,CAAC,GAAG,IAAI,6BAAqB,CAAC,aAAa,EAAW,CAAC;AAErF;;GAEG;AACU,QAAA,SAAS,GACpB,GAAG,iCAAyB,CAAC,GAAG,IAAI,6BAAqB,CAAC,KAAK,EAAW,CAAC;AAE7E;;GAEG;AACU,QAAA,SAAS,GACpB,GAAG,iCAAyB,CAAC,IAAI,IAAI,8BAAsB,CAAC,IAAI,EAAW,CAAC;AAE9E;;GAEG;AACU,QAAA,gBAAgB,GAC3B,GAAG,iCAAyB,CAAC,IAAI,IAAI,8BAAsB,CAAC,WAAW,EAAW,CAAC;AAErF;;GAEG;AACU,QAAA,eAAe,GAC1B,GAAG,iCAAyB,CAAC,IAAI,IAAI,8BAAsB,CAAC,UAAU,EAAW,CAAC;AAEpF;;;GAGG;AACU,QAAA,gBAAgB,GAC3B,GAAG,iCAAyB,CAAC,IAAI,IAAI,8BAAsB,CAAC,WAAW,EAAW,CAAC;AAErF;;GAEG;AACU,QAAA,UAAU,GACrB,GAAG,iCAAyB,CAAC,OAAO,IAAI,iCAAyB,CAAC,EAAE,EAAW,CAAC;AAElF;;GAEG;AACU,QAAA,OAAO,GAClB,GAAG,iCAAyB,CAAC,IAAI,IAAI,8BAAsB,CAAC,EAAE,EAAW,CAAC;AAE5E;;GAEG;AACU,QAAA,wBAAwB,GACnC,GAAG,iCAAyB,CAAC,QAAQ,IAAI,kCAA0B,CAAC,eAAe,EAAW,CAAC;AAEjG;;GAEG;AACU,QAAA,yBAAyB,GACpC,GAAG,iCAAyB,CAAC,QAAQ,IAAI,kCAA0B,CAAC,gBAAgB,EAAW,CAAC;AAElG;;GAEG;AACU,QAAA,cAAc,GACzB,GAAG,iCAAyB,CAAC,QAAQ,IAAI,kCAA0B,CAAC,KAAK,EAAW,CAAC;AAEvF;;GAEG;AACU,QAAA,mBAAmB,GAC9B,GAAG,iCAAyB,CAAC,QAAQ,IAAI,kCAA0B,CAAC,UAAU,EAAW,CAAC;AAE5F;;GAEG;AACU,QAAA,cAAc,GACzB,GAAG,iCAAyB,CAAC,QAAQ,IAAI,kCAA0B,CAAC,KAAK,EAAW,CAAC;AAEvF;;GAEG;AACU,QAAA,QAAQ,GAAG,UAAmB,CAAC;AAE5C;;GAEG;AACU,QAAA,uBAAuB,GAClC,GAAG,sBAAsB,UAAmB,CAAC;AAE/C;;GAEG;AACU,QAAA,QAAQ,GACnB,GAAG,iCAAyB,CAAC,GAAG,IAAI,6BAAqB,CAAC,IAAI,EAAW,CAAC;AAE5E;;GAEG;AACU,QAAA,SAAS,GACpB,GAAG,iCAAyB,CAAC,KAAK,IAAI,gCAAwB,CAAC,GAAG,EAAW,CAAC;AAEhF;;GAEG;AACU,QAAA,eAAe,GAC1B,GAAG,iCAAyB,CAAC,KAAK,IAAI,gCAAwB,CAAC,SAAS,EAAW,CAAC;AAEtF;;GAEG;AACU,QAAA,gBAAgB,GAC3B,GAAG,iCAAyB,CAAC,KAAK,IAAI,gCAAwB,CAAC,UAAU,EAAW,CAAC;AAE1E,QAAA,mBAAmB,GAAG;IACjC,SAAS,EAAT,iBAAS;IACT,WAAW,EAAX,mBAAW;IACX,eAAe,EAAf,uBAAe;IACf,YAAY,EAAZ,oBAAY;IACZ,gBAAgB,EAAhB,wBAAgB;IAChB,kBAAkB,EAAlB,0BAAkB;IAClB,mBAAmB,EAAnB,2BAAmB;IACnB,cAAc,EAAd,sBAAc;IACd,WAAW,EAAX,mBAAW;IACX,yBAAyB,EAAzB,iCAAyB;IACzB,0BAA0B,EAA1B,kCAA0B;IAC1B,sBAAsB,EAAtB,8BAAsB;IACtB,qBAAqB,EAArB,6BAAqB;IACrB,UAAU,EAAV,kBAAU;IACV,YAAY,EAAZ,oBAAY;IACZ,SAAS,EAAT,iBAAS;IACT,YAAY,EAAZ,oBAAY;IACZ,YAAY,EAAZ,oBAAY;IACZ,kBAAkB,EAAlB,0BAAkB;IAClB,oBAAoB,EAApB,4BAAoB;IACpB,YAAY,EAAZ,oBAAY;IACZ,uBAAuB,EAAvB,+BAAuB;IACvB,iCAAiC,EAAjC,yCAAiC;IACjC,0BAA0B,EAA1B,kCAA0B;IAC1B,oCAAoC,EAApC,4CAAoC;IACpC,eAAe,EAAf,uBAAe;IACf,gBAAgB,EAAhB,wBAAgB;IAChB,qBAAqB,EAArB,6BAAqB;IACrB,oBAAoB,EAApB,4BAAoB;IACpB,oBAAoB,EAApB,4BAAoB;IACpB,WAAW,EAAX,mBAAW;IACX,gBAAgB,EAAhB,wBAAgB;IAChB,cAAc,EAAd,sBAAc;IACd,iBAAiB,EAAjB,yBAAiB;IACjB,oBAAoB,EAApB,4BAAoB;IACpB,cAAc,EAAd,sBAAc;IACd,oBAAoB,EAApB,4BAAoB;IACpB,gBAAgB,EAAhB,wBAAgB;IAChB,gBAAgB,EAAhB,wBAAgB;IAChB,SAAS,EAAT,iBAAS;IACT,eAAe,EAAf,uBAAe;IACf,gBAAgB,EAAhB,wBAAgB;IAChB,yBAAyB,EAAzB,iCAAyB;IACzB,wBAAwB,EAAxB,gCAAwB;IACxB,uBAAuB,EAAvB,+BAAuB;IACvB,wBAAwB,EAAxB,gCAAwB;IACxB,yBAAyB,EAAzB,iCAAyB;IACzB,cAAc,EAAd,sBAAc;IACd,mBAAmB,EAAnB,2BAAmB;IACnB,cAAc,EAAd,sBAAc;IACd,iBAAiB,EAAjB,yBAAiB;IACjB,mBAAmB,EAAnB,2BAAmB;IACnB,UAAU,EAAV,kBAAU;IACV,OAAO,EAAP,eAAO;IACP,QAAQ,EAAR,gBAAQ;IACR,QAAQ,EAAR,gBAAQ;IACR,uBAAuB,EAAE,GAAG,iCAAyB,CAAC,aAAa,YAAY;CACvE,CAAC;AAEX,IAAY,qBAUX;AAVD,WAAY,qBAAqB;IAC/B,oCAAW,CAAA;IACX,wCAAe,CAAA;IACf,sCAAa,CAAA;IACb,gDAAuB,CAAA;IACvB,8CAAqB,CAAA;IACrB,gDAAuB,CAAA;IACvB,wCAAe,CAAA;IACf,gDAAuB,CAAA;IACvB,gDAAuB,CAAA;AACzB,CAAC,EAVW,qBAAqB,qCAArB,qBAAqB,QAUhC;AAED;;GAEG;AACH,IAAY,QAIX;AAJD,WAAY,QAAQ;IAClB,+BAAmB,CAAA;IACnB,qCAAyB,CAAA;IACzB,mCAAuB,CAAA;AACzB,CAAC,EAJW,QAAQ,wBAAR,QAAQ,QAInB;AAED,IAAY,SAMX;AAND,WAAY,SAAS;IACnB,8BAAiB,CAAA;IACjB,oCAAuB,CAAA;IACvB,oCAAuB,CAAA;IACvB,8BAAiB,CAAA;IACjB,kCAAqB,CAAA;AACvB,CAAC,EANW,SAAS,yBAAT,SAAS,QAMpB;AAED,IAAY,WASX;AATD,WAAY,WAAW;IACrB,gCAAiB,CAAA;IACjB,sCAAuB,CAAA;IACvB,sCAAuB,CAAA;IACvB,gCAAiB,CAAA;IACjB,iCAAiC;IACjC,gCAAiB,CAAA;IACjB,0BAAW,CAAA;IACX,8BAAe,CAAA;AACjB,CAAC,EATW,WAAW,2BAAX,WAAW,QAStB"}