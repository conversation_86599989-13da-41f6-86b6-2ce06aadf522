{"program": {"fileNames": ["../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es5.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2015.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2016.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2017.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2018.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2019.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2020.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2021.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2022.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2023.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.esnext.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2015.core.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2016.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2017.date.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2017.object.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2017.string.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2019.array.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2019.object.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2019.string.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2020.date.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2020.string.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2020.number.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2021.promise.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2021.string.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2021.weakref.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2021.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2022.array.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2022.error.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2022.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2022.object.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2022.sharedmemory.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2022.string.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2022.regexp.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2023.array.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2023.collection.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.es2023.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.esnext.array.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.esnext.collection.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.esnext.disposable.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.esnext.string.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.esnext.promise.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.esnext.decorators.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.esnext.object.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.esnext.regexp.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.decorators.d.ts", "../../../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript/lib/lib.decorators.legacy.d.ts", "../src/trace/SemanticConventions.ts", "../src/trace/index.ts", "../src/resource/SemanticResourceAttributes.ts", "../src/resource/index.ts", "../src/index.ts", "../../../node_modules/.pnpm/@jest+expect-utils@29.7.0/node_modules/@jest/expect-utils/build/index.d.ts", "../../../node_modules/.pnpm/chalk@4.1.2/node_modules/chalk/index.d.ts", "../../../node_modules/.pnpm/@sinclair+typebox@0.27.8/node_modules/@sinclair/typebox/typebox.d.ts", "../../../node_modules/.pnpm/@jest+schemas@29.6.3/node_modules/@jest/schemas/build/index.d.ts", "../../../node_modules/.pnpm/pretty-format@29.7.0/node_modules/pretty-format/build/index.d.ts", "../../../node_modules/.pnpm/jest-diff@29.7.0/node_modules/jest-diff/build/index.d.ts", "../../../node_modules/.pnpm/jest-matcher-utils@29.7.0/node_modules/jest-matcher-utils/build/index.d.ts", "../../../node_modules/.pnpm/expect@29.7.0/node_modules/expect/build/index.d.ts", "../../../node_modules/.pnpm/@types+jest@29.5.12/node_modules/@types/jest/index.d.ts"], "fileInfos": [{"version": "44e584d4f6444f58791784f1d530875970993129442a847597db702a073ca68c", "affectsGlobalScope": true}, "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "9a68c0c07ae2fa71b44384a839b7b8d81662a236d4b9ac30916718f7510b1b2d", "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "feecb1be483ed332fad555aff858affd90a48ab19ba7272ee084704eb7167569", "5514e54f17d6d74ecefedc73c504eadffdeda79c7ea205cf9febead32d45c4bc", "27bdc30a0e32783366a5abeda841bc22757c1797de8681bbe81fbc735eeb1c10", "17edc026abf73c5c2dd508652d63f68ec4efd9d4856e3469890d27598209feb5", {"version": "6920e1448680767498a0b77c6a00a8e77d14d62c3da8967b171f1ddffa3c18e4", "affectsGlobalScope": true}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true}, {"version": "4443e68b35f3332f753eacc66a04ac1d2053b8b035a0e0ac1d455392b5e243b3", "affectsGlobalScope": true}, {"version": "bc47685641087c015972a3f072480889f0d6c65515f12bd85222f49a98952ed7", "affectsGlobalScope": true}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true}, {"version": "93495ff27b8746f55d19fcbcdbaccc99fd95f19d057aed1bd2c0cafe1335fbf0", "affectsGlobalScope": true}, {"version": "6fc23bb8c3965964be8c597310a2878b53a0306edb71d4b5a4dfe760186bcc01", "affectsGlobalScope": true}, {"version": "ea011c76963fb15ef1cdd7ce6a6808b46322c527de2077b6cfdf23ae6f5f9ec7", "affectsGlobalScope": true}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true}, {"version": "bb42a7797d996412ecdc5b2787720de477103a0b2e53058569069a0e2bae6c7e", "affectsGlobalScope": true}, {"version": "4738f2420687fd85629c9efb470793bb753709c2379e5f85bc1815d875ceadcd", "affectsGlobalScope": true}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true}, {"version": "9fc46429fbe091ac5ad2608c657201eb68b6f1b8341bd6d670047d32ed0a88fa", "affectsGlobalScope": true}, {"version": "61c37c1de663cf4171e1192466e52c7a382afa58da01b1dc75058f032ddf0839", "affectsGlobalScope": true}, {"version": "b541a838a13f9234aba650a825393ffc2292dc0fc87681a5d81ef0c96d281e7a", "affectsGlobalScope": true}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true}, {"version": "ae37d6ccd1560b0203ab88d46987393adaaa78c919e51acf32fb82c86502e98c", "affectsGlobalScope": true}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true}, {"version": "bf14a426dbbf1022d11bd08d6b8e709a2e9d246f0c6c1032f3b2edb9a902adbe", "affectsGlobalScope": true}, {"version": "5e07ed3809d48205d5b985642a59f2eba47c402374a7cf8006b686f79efadcbd", "affectsGlobalScope": true}, {"version": "2b72d528b2e2fe3c57889ca7baef5e13a56c957b946906d03767c642f386bbc3", "affectsGlobalScope": true}, {"version": "479553e3779be7d4f68e9f40cdb82d038e5ef7592010100410723ceced22a0f7", "affectsGlobalScope": true}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true}, {"version": "af3dd424cf267428f30ccfc376f47a2c0114546b55c44d8c0f1d57d841e28d74", "affectsGlobalScope": true}, {"version": "995c005ab91a498455ea8dfb63aa9f83fa2ea793c3d8aa344be4a1678d06d399", "affectsGlobalScope": true}, {"version": "d3d7b04b45033f57351c8434f60b6be1ea71a2dfec2d0a0c3c83badbb0e3e693", "affectsGlobalScope": true}, {"version": "956d27abdea9652e8368ce029bb1e0b9174e9678a273529f426df4b3d90abd60", "affectsGlobalScope": true}, {"version": "4fa6ed14e98aa80b91f61b9805c653ee82af3502dc21c9da5268d3857772ca05", "affectsGlobalScope": true}, {"version": "e6633e05da3ff36e6da2ec170d0d03ccf33de50ca4dc6f5aeecb572cedd162fb", "affectsGlobalScope": true}, {"version": "d8670852241d4c6e03f2b89d67497a4bbefe29ecaa5a444e2c11a9b05e6fccc6", "affectsGlobalScope": true}, {"version": "8444af78980e3b20b49324f4a16ba35024fef3ee069a0eb67616ea6ca821c47a", "affectsGlobalScope": true}, {"version": "caccc56c72713969e1cfe5c3d44e5bab151544d9d2b373d7dbe5a1e4166652be", "affectsGlobalScope": true}, {"version": "3287d9d085fbd618c3971944b65b4be57859f5415f495b33a6adc994edd2f004", "affectsGlobalScope": true}, {"version": "b4b67b1a91182421f5df999988c690f14d813b9850b40acd06ed44691f6727ad", "affectsGlobalScope": true}, {"version": "9d540251809289a05349b70ab5f4b7b99f922af66ab3c39ba56a475dcf95d5ff", "affectsGlobalScope": true}, {"version": "436aaf437562f276ec2ddbee2f2cdedac7664c1e4c1d2c36839ddd582eeb3d0a", "affectsGlobalScope": true}, {"version": "8e3c06ea092138bf9fa5e874a1fdbc9d54805d074bee1de31b99a11e2fec239d", "affectsGlobalScope": true}, {"version": "0b11f3ca66aa33124202c80b70cd203219c3d4460cfc165e0707aa9ec710fc53", "affectsGlobalScope": true}, {"version": "6a3f5a0129cc80cf439ab71164334d649b47059a4f5afca90282362407d0c87f", "affectsGlobalScope": true}, {"version": "811c71eee4aa0ac5f7adf713323a5c41b0cf6c4e17367a34fbce379e12bbf0a4", "affectsGlobalScope": true}, {"version": "15b98a533864d324e5f57cd3cfc0579b231df58c1c0f6063ea0fcb13c3c74ff9", "affectsGlobalScope": true}, {"version": "0a6282c8827e4b9a95f4bf4f5c205673ada31b982f50572d27103df8ceb8013c", "affectsGlobalScope": true}, {"version": "ac77cb3e8c6d3565793eb90a8373ee8033146315a3dbead3bde8db5eaf5e5ec6", "affectsGlobalScope": true}, {"version": "d4b1d2c51d058fc21ec2629fff7a76249dec2e36e12960ea056e3ef89174080f", "affectsGlobalScope": true}, {"version": "2fef54945a13095fdb9b84f705f2b5994597640c46afeb2ce78352fab4cb3279", "affectsGlobalScope": true}, {"version": "56e4ed5aab5f5920980066a9409bfaf53e6d21d3f8d020c17e4de584d29600ad", "affectsGlobalScope": true}, {"version": "33358442698bb565130f52ba79bfd3d4d484ac85fe33f3cb1759c54d18201393", "affectsGlobalScope": true}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true}, {"version": "d441f0ad764da2f57dd545b2560e524c054a677c163c2cd6a332eb9dd1e38d1c", "signature": "9d1bae7f30b33a68b22f3b09064147976ac263e4acca3974c11acb2d541e9ec2"}, "8a7d376e540650503af8061824b3f5dfe17c9ae58b9010a0d9cb0266a410d248", {"version": "979a484f31bcf7c66d798aa35e8e488c3f15b6572ada391fea1449ac1791ad80", "signature": "7743b59242f1f4c1bc64871a0c45dc4d6170e4abbb825a63d418eb2699c0628b"}, "1130ac1eefbaefb77155f3fa02ab3d39e6f279b897b58d002b201deb7d030617", "7bdd2792145b3a051f0fb43ad57f69632393da97b7af8fd3aa09c11fe9ff4005", "cdcc132f207d097d7d3aa75615ab9a2e71d6a478162dde8b67f88ea19f3e54de", "0d14fa22c41fdc7277e6f71473b20ebc07f40f00e38875142335d5b63cdfc9d2", "c085e9aa62d1ae1375794c1fb927a445fa105fed891a7e24edbb1c3300f7384a", "f315e1e65a1f80992f0509e84e4ae2df15ecd9ef73df975f7c98813b71e4c8da", "5b9586e9b0b6322e5bfbd2c29bd3b8e21ab9d871f82346cb71020e3d84bae73e", "3e70a7e67c2cb16f8cd49097360c0309fe9d1e3210ff9222e9dac1f8df9d4fb6", "ab68d2a3e3e8767c3fba8f80de099a1cfc18c0de79e42cb02ae66e22dfe14a66", "d96cc6598148bf1a98fb2e8dcf01c63a4b3558bdaec6ef35e087fd0562eb40ec", {"version": "5ab630d466ac55baa6d32820378098404fc18ba9da6f7bc5df30c5dbb1cffae8", "affectsGlobalScope": true}], "root": [[72, 76]], "options": {"composite": true, "declaration": true, "declarationMap": true, "esModuleInterop": true, "module": 1, "outDir": "./", "rootDir": "..", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": 3}, "fileIdsList": [[79], [81, 84], [77, 83], [81], [78, 82], [80], [73, 75], [74], [72]], "referencedMap": [[80, 1], [85, 2], [84, 3], [82, 4], [83, 5], [81, 6], [76, 7], [75, 8], [73, 9]], "latestChangedDtsFile": "./src/index.d.ts"}, "version": "5.5.4"}