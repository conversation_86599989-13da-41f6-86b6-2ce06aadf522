{"name": "console-table-printer", "version": "2.14.6", "repository": "github:console-table-printer/console-table-printer", "description": "Printing pretty tables on console log", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"setup": "yarn", "build": "tsc", "format": "prettier --write \"**/*.{json,ts,tsx,yml,js,jsx}\"", "test": "jest --config jestconfig.json", "test:coverage": "jest --config jestconfig.json --coverage", "lint": "eslint .", "semantic-release": "semantic-release"}, "keywords": ["console-table", "console-log", "print-table", "node-table-printing"], "files": ["dist"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@eslint/js": "^9.28.0", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@types/jest": "^29.5.14", "@types/node": "^24.0.3", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "husky": "^9.1.7", "jest": "^29.7.0", "prettier": "^3.5.3", "pretty-quick": "^4.2.2", "semantic-release": "^24.2.5", "ts-jest": "^29.3.4", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1"}, "homepage": "https://console-table.netlify.app", "dependencies": {"simple-wcswidth": "^1.0.1"}}