import { ALIGNMENT, CharLengthDict } from '../models/common';
export declare const splitTextIntoTextsOfMinLen: (inpStr: string, width: number, charLength?: CharLengthDict) => string[];
export declare const textWithPadding: (text: string, alignment: ALIGNMENT, columnLen: number, charLength?: CharLengthDict) => string;
export declare const biggestWordInSentence: (inpStr: string, charLength?: CharLengthDict) => number;
