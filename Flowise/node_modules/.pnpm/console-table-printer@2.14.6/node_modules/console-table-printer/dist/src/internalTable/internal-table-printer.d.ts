import { Dictionary } from '../models/common';
import { ComplexOptions } from '../models/external-table';
import TableInternal from './internal-table';
export declare const renderTable: (table: TableInternal) => string;
export declare const renderSimpleTable: (rows: Dictionary[], tableOptions?: ComplexOptions) => string;
export declare const printSimpleTable: (rows: Dictionary[], tableOptions?: ComplexOptions) => void;
