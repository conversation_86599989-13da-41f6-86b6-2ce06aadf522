{"name": "p-timeout", "version": "3.2.0", "description": "Timeout a promise after a specified amount of time", "license": "MIT", "repository": "sindresorhus/p-timeout", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "timeout", "error", "invalidate", "async", "await", "promises", "time", "out", "cancel", "bluebird"], "dependencies": {"p-finally": "^1.0.0"}, "devDependencies": {"ava": "^1.4.1", "delay": "^4.1.0", "p-cancelable": "^2.0.0", "tsd": "^0.7.2", "xo": "^0.24.0"}}